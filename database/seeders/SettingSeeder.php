<?php

declare(strict_types=1);

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class SettingSeeder extends Seeder
{
    public function run(): void
    {
        $settings = [
            'memorization' => [
                'weight' => 60,
                'deduction_per_mistake' => 2,
                'fail_threshold' => 15,
            ],
            'tajweed_rules' => [
                'weight' => 12.5,
                'deduction_per_mistake' => 0.5,
                'fail_threshold' => 5,
            ],
            'narration_principles' => [
                'weight' => 10,
                'deduction_per_mistake' => 1,
                'fail_threshold' => 5,
            ],
            'stopping_starting_rules' => [
                'weight' => 5,
                'deduction_per_mistake' => 0.5,
                'fail_threshold' => 5,
            ],
            'recitation_performance' => [
                'weight' => 7.5,
                'deduction_per_mistake' => 1,
                'fail_threshold' => 5,
            ],
            'sound' => [
                'weight' => 5,
                'deduction_per_mistake' => 0.25,
                'fail_threshold' => 5,
            ],
        ];
    }
}
