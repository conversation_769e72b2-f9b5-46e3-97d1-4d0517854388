<?php

declare(strict_types=1);

namespace App\Filament\Resources\Exams\Pages;

use App\Filament\Resources\Exams\ExamResource;
use App\Models\ExamStudentQuestion;
use App\Models\ExamSettingProfile;
use App\Models\Setting;
use BackedEnum;
use Filament\Actions\Action;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Schemas\Components\Section;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Grouping\Group;
use Filament\Tables\Table;

class ManageResults extends ManageRelatedRecords
{
    protected static string $resource = ExamResource::class;

    protected static string $relationship = 'studentQuestions';

    protected static string|BackedEnum|null $navigationIcon = Heroicon::BookOpen;

    public static function getNavigationLabel(): string
    {
        return __('Manage Results');
    }

    public function getTitle(): string
    {
        return __('Manage Results');
    }

    public function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(
                fn($query) => $query->with(['student', 'question', 'ratings']),
            )
            ->groups([Group::make('student.name')->label(__('Student'))])
            ->defaultGroup('student.name')
            ->columns($this->getDynamicColumns())
            ->recordActions([
                Action::make('editResult')
                    ->label('') // Remove label for icon-only
                    ->tooltip(__('Edit Result')) // Optional hover tooltip
                    ->icon('heroicon-o-pencil-square')
                    ->iconButton()
                    ->color('primary')
                    ->slideOver()
                    ->schema(function (ExamStudentQuestion $record) {
                        return $this->getDynamicFormFields($record);
                    })
                    ->action(function (
                        array $data,
                        ExamStudentQuestion $record,
                    ) {
                        foreach ($data as $aspect => $mistakes) {
                            $record
                                ->ratings()
                                ->updateOrCreate(
                                    ['aspect' => $aspect],
                                    ['mistakes' => $mistakes],
                                );
                        }
                    }),
                Action::make('resetQuestion')
                    ->label('')
                    ->tooltip(__('Reset Question'))
                    ->icon('heroicon-o-trash')
                    ->iconButton()
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading(__('Reset this Question?'))
                    ->modalDescription(
                        __(
                            'This will delete all ratings for this question. Are you sure?',
                        ),
                    )
                    ->action(function (ExamStudentQuestion $record) {
                        $record->ratings()->delete();
                    }),
            ]);
    }

    protected function getDynamicColumns(): array
    {
        $columns = [
            TextColumn::make('question_label')
                ->label('Question')
                ->translateLabel()
                ->state(function (ExamStudentQuestion $record) {
                    $question = $record->question;
                    return $question->title;
                }),
        ];

        // Get the exam setting profile
        $exam = $this->getRecord();
        $profile = $exam->examSettingProfile ?? ExamSettingProfile::getActive();

        if ($profile) {
            // Group aspects by parent - only show parent aspects in columns
            foreach ($profile->aspects as $aspect) {
                if ($aspect->has_sub_aspects) {
                    // For grouped aspects, show parent aspect score (calculated from deductions)
                    $columns[] = TextColumn::make($aspect->key . '_total_score')
                        ->label(
                            $aspect->label .
                                ' (' .
                                number_format($aspect->weight, 1) .
                                '%)',
                        )
                        ->state(function (ExamStudentQuestion $record) use (
                            $aspect,
                        ) {
                            $parent = $record->ratings->firstWhere(
                                'aspect',
                                $aspect->key,
                            );

                            // If the parent rating exists, show it; otherwise show '-' (or 0 if you prefer)
                            return $parent
                                ? number_format((float) $parent->score, 2)
                                : '-';
                        })

                        ->tooltip(function (ExamStudentQuestion $record) use (
                            $aspect,
                        ) {
                            // Show breakdown in tooltip with deductions
                            $breakdown = [];
                            $breakdown[] =
                                __('Max Score') .
                                ': ' .
                                number_format($aspect->weight, 2);

                            $totalDeductions = 0;
                            $subAspectDetails = [];

                            foreach ($aspect->subAspects as $subAspect) {
                                $rating = $record->ratings->firstWhere(
                                    'aspect',
                                    $subAspect->key,
                                );
                                if ($rating) {
                                    $deduction = abs($rating->score); // Convert from negative storage
                                    $mistakes = $rating->mistakes;
                                    $totalDeductions += $deduction;
                                    $subAspectDetails[] =
                                        $subAspect->label .
                                        ': ' .
                                        $mistakes .
                                        ' mistakes (-' .
                                        number_format($deduction, 2) .
                                        ')';
                                } else {
                                    $subAspectDetails[] =
                                        $subAspect->label .
                                        ': 0 mistakes (-0.00)';
                                }
                            }

                            $parentRating = $record->ratings->firstWhere(
                                'aspect',
                                $aspect->key,
                            );
                            $finalScore = $parentRating
                                ? $parentRating->score
                                : 0;

                            $breakdown[] = '---';
                            $breakdown = array_merge(
                                $breakdown,
                                $subAspectDetails,
                            );
                            $breakdown[] = '---';
                            $breakdown[] =
                                __('Total Deductions') .
                                ': -' .
                                number_format($totalDeductions, 2);
                            $breakdown[] =
                                __('Final Score') .
                                ': ' .
                                number_format($finalScore, 2);

                            return implode(' | ', $breakdown);
                        })
                        ->sortable(false);
                } else {
                    // For flat aspects, show individual aspect column
                    $columns[] = TextColumn::make($aspect->key . '_score')
                        ->label(
                            $aspect->label .
                                ' (' .
                                number_format($aspect->weight, 1) .
                                '%)',
                        )
                        ->state(function (ExamStudentQuestion $record) use (
                            $aspect,
                        ) {
                            $rating = $record->ratings->firstWhere(
                                'aspect',
                                $aspect->key,
                            );
                            return $rating
                                ? number_format($rating->score, 2)
                                : '-';
                        })
                        ->tooltip(function (ExamStudentQuestion $record) use (
                            $aspect,
                        ) {
                            return __('Max Score') .
                                ': ' .
                                number_format($aspect->weight, 2);
                        })
                        ->sortable(false);
                }
            }
        }

        // Add the total score column
        $columns[] = TextColumn::make('total_score')
            ->label(__('Total Score'))
            ->state(function (ExamStudentQuestion $record) {
                // Sum only ratings that actually carry weight (parents + flat aspects)
                $total = $record->ratings
                    ->where('weight', '>', 0)
                    ->sum('score');
                return number_format((float) $total, 2);
            })
            ->sortable(false);

        return $columns;
    }

    protected function getDynamicFormFields(ExamStudentQuestion $record): array
    {
        $ratings = $record->ratings->keyBy('aspect');

        // Get the exam setting profile
        $exam = $this->getRecord();
        $profile = $exam->examSettingProfile ?? ExamSettingProfile::getActive();

        if (!$profile) {
            return [];
        }

        $fields = [];

        foreach ($profile->aspects as $aspect) {
            if ($aspect->has_sub_aspects) {
                // Build sub‑aspect inputs
                $subFields = [];

                foreach ($aspect->subAspects as $subAspect) {
                    $deduction = (float) $subAspect->deduction_per_mistake;
                    $failThreshold = (int) $subAspect->fail_threshold;
                    $parentWeight = (float) $aspect->weight;

                    $maxMistakesText = __('N/A');
                    $maxMistakesValue = 9999;

                    if ($deduction > 0) {
                        $calculatedMax = (int) floor(
                            $parentWeight / $deduction,
                        );
                        $maxMistakesText = (string) $calculatedMax;
                        $maxMistakesValue = $calculatedMax;
                    }

                    $helperText =
                        __('Parent Aspect') .
                        ': ' .
                        $aspect->label .
                        ' (' .
                        $parentWeight .
                        '%) | ' .
                        __('Deduction Per Mistake') .
                        ': ' .
                        $deduction;

                    if ($failThreshold > 0) {
                        $helperText .=
                            ' | ' .
                            __('Fail Threshold') .
                            ': ' .
                            $failThreshold;
                    }

                    if ($aspect->fail_aspect_key === $subAspect->key) {
                        $helperText .=
                            ' | ' .
                            __('FAIL ASPECT - Any mistake causes failure');
                    }

                    $subFields[] = TextInput::make($subAspect->key)
                        ->label($subAspect->label)
                        ->prefix(__('Max Mistakes') . ': ' . $maxMistakesText)
                        ->suffix('-' . $deduction)
                        ->helperText($helperText)
                        ->minValue(0)
                        ->maxValue($maxMistakesValue)
                        ->numeric()
                        ->step(0.01)
                        ->default($ratings[$subAspect->key]?->mistakes ?? 0);
                }

                // Wrap sub‑aspects in a section with parent info
                $sectionDescription =
                    __('Weight') . ': ' . (float) $aspect->weight . '%';
                if (!empty($aspect->fail_aspect_key)) {
                    $sectionDescription .= ' | ' . __('Has a fail sub‑aspect');
                }

                $fields[] = Section::make($aspect->label)
                    ->description($sectionDescription)
                    ->schema($subFields)
                    ->columns(1) // tweak if you want multi‑column inside the section
                    ->collapsible(); // optional: collapsible for cleaner UI
            } else {
                // Flat aspect (no sub‑aspects) -> single field
                $weight = (float) $aspect->weight;
                $deduction = (float) $aspect->deduction_per_mistake;
                $failThreshold = (int) $aspect->fail_threshold;

                $maxMistakesText = __('N/A');
                $maxMistakesValue = 9999;

                if ($deduction > 0) {
                    $calculatedMax = (int) floor($weight / $deduction);
                    $maxMistakesText = (string) $calculatedMax;
                    $maxMistakesValue = $calculatedMax;
                }

                $helperText =
                    __('Weight') .
                    ': ' .
                    $weight .
                    '% | ' .
                    __('Deduction Per Mistake') .
                    ': ' .
                    $deduction;

                if ($failThreshold > 0) {
                    $helperText .=
                        ' | ' . __('Fail Threshold') . ': ' . $failThreshold;
                }

                $fields[] = TextInput::make($aspect->key)
                    ->label($aspect->label)
                    ->prefix(__('Max Mistakes') . ': ' . $maxMistakesText)
                    ->suffix('-' . $deduction)
                    ->helperText($helperText)
                    ->minValue(0)
                    ->maxValue($maxMistakesValue)
                    ->numeric()
                    ->step(1)
                    ->default($ratings[$aspect->key]?->mistakes ?? 0);
            }
        }

        return $fields;
    }

    /**
     * Calculate the maximum possible score for a grouped aspect
     */
    protected function getMaxPossibleScoreForAspect($aspect): float
    {
        if (!$aspect->has_sub_aspects) {
            return (float) $aspect->weight;
        }

        // For grouped aspects, the max score is the parent aspect's weight
        // This represents the total weight allocated to all sub-aspects combined
        return (float) $aspect->weight;
    }

    /**
     * Get the current total score for a grouped aspect
     */
    protected function getCurrentScoreForAspect(
        ExamStudentQuestion $record,
        $aspect,
    ): float {
        if (!$aspect->has_sub_aspects) {
            $rating = $record->ratings->firstWhere('aspect', $aspect->key);
            return $rating ? (float) $rating->score : 0;
        }

        // For grouped aspects, sum all sub-aspect scores
        $totalScore = 0;
        foreach ($aspect->subAspects as $subAspect) {
            $rating = $record->ratings->firstWhere('aspect', $subAspect->key);
            if ($rating) {
                $totalScore += (float) $rating->score;
            }
        }
        return $totalScore;
    }
}
