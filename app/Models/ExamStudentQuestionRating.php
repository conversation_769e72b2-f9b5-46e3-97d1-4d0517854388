<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ExamStudentQuestionRating extends Model
{
    use HasUuids;

    protected $fillable = [
        'exam_student_question_id',
        'aspect',
        'mistakes',
        'score',
        'weight',
        'deduction_per_mistake',
    ];

    public function questionAssignment(): BelongsTo
    {
        return $this->belongsTo(
            ExamStudentQuestion::class,
            'exam_student_question_id',
        );
    }

    protected static function booted(): void
    {
        // --- Calculate row values before save ---
        static::saving(function (self $rating) {
            $question = $rating->questionAssignment;
            $exam = $question?->exam;

            // Resolve settings profile (per exam, fallback to active)
            $profile =
                $exam?->examSettingProfile ??
                \App\Models\ExamSettingProfile::query()
                    ->where('is_active', true)
                    ->first();

            if (!$profile) {
                // Safe defaults
                $rating->weight = 0;
                $rating->deduction_per_mistake = 0;
                $rating->fail_threshold = null;
                $rating->score = 0;
                return;
            }

            // Case 1: This rating's aspect matches a PARENT aspect key
            $parent = $profile
                ->aspects()
                ->where('key', $rating->aspect)
                ->with('subAspects')
                ->first();

            if ($parent) {
                if ($parent->has_sub_aspects) {
                    // Parent row is computed from child rows
                    $subs = $parent->subAspects->keyBy('key');

                    // Pull current children stored for this question
                    $children =
                        $question
                            ?->ratings()
                            ->whereIn('aspect', $subs->keys()->all())
                            ->get()
                            ->keyBy('aspect') ?? collect();

                    $totalMistakes = 0;
                    $totalDeduction = 0.0;

                    foreach ($subs as $subKey => $sub) {
                        $m = (int) ($children->get($subKey)?->mistakes ?? 0);
                        $per = (float) $sub->deduction_per_mistake;
                        $totalMistakes += $m;
                        $totalDeduction += $m * $per;
                    }

                    // Zeroing rule (determines-degree)
                    $failThreshold = null;
                    if (
                        $parent->fail_aspect_key &&
                        $subs->has($parent->fail_aspect_key)
                    ) {
                        $failThreshold =
                            (int) $subs[$parent->fail_aspect_key]
                                ->fail_threshold;
                        $failMistakes =
                            (int) ($children->get($parent->fail_aspect_key)
                                ?->mistakes ?? 0);
                        if (
                            $failThreshold > 0 &&
                            $failMistakes >= $failThreshold
                        ) {
                            $rating->weight = (float) $parent->weight;
                            $rating->deduction_per_mistake = null;
                            $rating->fail_threshold = $failThreshold;
                            $rating->mistakes = $totalMistakes; // sum of all children
                            $rating->score = 0.0;
                            return;
                        }
                    }

                    $weight = (float) $parent->weight;
                    $score = max(0.0, min($weight, $weight - $totalDeduction));

                    $rating->weight = $weight;
                    $rating->deduction_per_mistake = null; // not used for parent
                    $rating->fail_threshold = $failThreshold; // informational
                    $rating->mistakes = $totalMistakes;
                    $rating->score = round($score, 2);
                    return;
                }

                // Flat parent aspect (no sub-aspects): classic formula
                $mistakes = (int) $rating->mistakes;
                $weight = (float) $parent->weight;
                $ded = (float) ($parent->deduction_per_mistake ?? 0);
                $thr = (int) ($parent->fail_threshold ?? 0);

                $rating->weight = $weight;
                $rating->deduction_per_mistake = $ded;
                $rating->fail_threshold = $thr;

                if ($thr > 0 && $mistakes >= $thr) {
                    $rating->score = 0.0;
                    return;
                }

                $rating->score = round(max(0, $weight - $mistakes * $ded), 2);
                return;
            }

            // Case 2: This looks like a CHILD sub-aspect row
            $parentForChild = $profile
                ->aspects()
                ->whereHas(
                    'subAspects',
                    fn($q) => $q->where('key', $rating->aspect),
                )
                ->with([
                    'subAspects' => fn($q) => $q->where('key', $rating->aspect),
                ])
                ->first();

            if ($parentForChild) {
                $sub = $parentForChild->subAspects->first();
                $mistakes = (int) $rating->mistakes;
                $per = (float) ($sub->deduction_per_mistake ?? 0);

                // Cap mistakes so this single sub-aspect cannot deduct beyond parent weight
                $parentWeight = (float) $parentForChild->weight;
                $maxMistakes =
                    $per > 0 ? (int) floor($parentWeight / $per) : PHP_INT_MAX;
                if ($mistakes > $maxMistakes) {
                    $mistakes = $maxMistakes;
                    $rating->mistakes = $mistakes;
                }

                $rating->weight = 0; // children carry no weight
                $rating->deduction_per_mistake = $per;
                $rating->fail_threshold = (int) ($sub->fail_threshold ?? 0);
                $rating->score = round($mistakes * $per, 2); // store deduction as +ve
                return;
            }

            // Case 3: Unknown aspect key -> neutralize
            $rating->weight = 0;
            $rating->deduction_per_mistake = 0;
            $rating->fail_threshold = null;
            $rating->score = 0.0;
        });

        // --- After save: if a CHILD was saved, recompute its PARENT row and upsert it ---
        static::saved(function (self $rating) {
            $question = $rating->questionAssignment;
            if (!$question) {
                return;
            }

            $exam = $question->exam;
            $profile =
                $exam?->examSettingProfile ??
                \App\Models\ExamSettingProfile::query()
                    ->where('is_active', true)
                    ->first();

            if ($profile) {
                // Is the saved aspect a child? If so, recompute parent
                $parent = $profile
                    ->aspects()
                    ->whereHas(
                        'subAspects',
                        fn($q) => $q->where('key', $rating->aspect),
                    )
                    ->with('subAspects')
                    ->first();

                if ($parent && $parent->has_sub_aspects) {
                    $subs = $parent->subAspects->keyBy('key');

                    // Reload children for this question
                    $children = $question
                        ->ratings()
                        ->whereIn('aspect', $subs->keys()->all())
                        ->get()
                        ->keyBy('aspect');

                    $totalMistakes = 0;
                    $totalDeduction = 0.0;

                    foreach ($subs as $subKey => $sub) {
                        $m = (int) ($children->get($subKey)?->mistakes ?? 0);
                        $per = (float) $sub->deduction_per_mistake;
                        $totalMistakes += $m;
                        $totalDeduction += $m * $per;
                    }

                    // Zeroing rule
                    $finalScore = max(
                        0,
                        (float) $parent->weight - $totalDeduction,
                    );
                    if (
                        $parent->fail_aspect_key &&
                        $subs->has($parent->fail_aspect_key)
                    ) {
                        $thr =
                            (int) $subs[$parent->fail_aspect_key]
                                ->fail_threshold;
                        $failMist =
                            (int) ($children->get($parent->fail_aspect_key)
                                ?->mistakes ?? 0);
                        if ($thr > 0 && $failMist >= $thr) {
                            $finalScore = 0.0;
                        }
                    }

                    // Upsert parent rating row
                    $question->ratings()->updateOrCreate(
                        ['aspect' => $parent->key],
                        [
                            'mistakes' => $totalMistakes,
                            'weight' => (float) $parent->weight,
                            'deduction_per_mistake' => null,
                            'fail_threshold' => $parent->fail_aspect_key
                                ? (int) ($subs[$parent->fail_aspect_key]
                                    ->fail_threshold ?? 0)
                                : null,
                            'score' => round($finalScore, 2),
                        ],
                    );
                }
            }

            // --- Keep your exam started / completed timestamps logic ---
            $examStudent = \App\Models\ExamStudent::where(
                'exam_id',
                $question->exam_id ?? null,
            )
                ->where('student_id', $question->student_id ?? null)
                ->first();

            if ($examStudent) {
                if (!$examStudent->is_started) {
                    $examStudent->is_started = true;
                    $examStudent->started_at = now();
                }

                $allRated = $examStudent
                    ->questions()
                    ->with('ratings')
                    ->get()
                    ->every(fn($q) => $q->ratings->isNotEmpty());

                if ($allRated && is_null($examStudent->completed_at)) {
                    $examStudent->completed_at = now();
                }

                if (
                    $examStudent->isDirty([
                        'is_started',
                        'started_at',
                        'completed_at',
                    ])
                ) {
                    $examStudent->save();
                }
            }
        });
    }
}
