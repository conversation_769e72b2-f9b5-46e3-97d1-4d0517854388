<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ExamStudentQuestion extends Model
{
    use HasUuids;

    protected $fillable = ['exam_id', 'student_id', 'question_id'];

    protected $appends = ['rating_summary'];

    public function exam(): BelongsTo
    {
        return $this->belongsTo(Exam::class);
    }

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    public function question(): BelongsTo
    {
        return $this->belongsTo(Question::class);
    }

    public function ratings(): HasMany
    {
        return $this->hasMany(ExamStudentQuestionRating::class);
    }

    public function calculateTotalRating(): array
    {
        // Only count parent/flat aspects (weight > 0)
        $parentRatings = $this->ratings->filter(
            fn($rating) => $rating->weight > 0,
        );

        $totalScore = $parentRatings->sum('score');
        $totalPossible = $parentRatings->sum('weight');

        return [
            'total_score' => round($totalScore, 2),
            'total_possible' => round($totalPossible, 2),
            'percentage' =>
                $totalPossible > 0
                    ? round(($totalScore / $totalPossible) * 100, 2)
                    : 0,
        ];
    }

    public function getRatingSummaryAttribute(): array
    {
        return $this->calculateTotalRating();
    }
}
